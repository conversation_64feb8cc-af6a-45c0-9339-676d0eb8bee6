// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id            String       @id @default(cuid())
  name          String? // Added for NextAuth
  email         String       @unique
  emailVerified DateTime? // Added for NextAuth
  image         String? // Added for NextAuth
  password      String
  passwordResetToken String? @unique
  passwordResetExpires DateTime?
  failedLoginAttempts Int @default(0)
  lockedUntil   DateTime?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  profile       Profile? // One-to-one relationship with Profile
  assessments   Assessment[] // One-to-many relationship with Assessment
  forumPosts    ForumPost[] // One-to-many relationship with ForumPost
  forumReplies  ForumReply[] // One-to-many relationship with ForumReply
  forumReactions ForumReaction[] // One-to-many relationship with ForumReaction
  freedomFund   FreedomFund? // One-to-one relationship with FreedomFund
  learningProgress UserLearningProgress[] // One-to-many relationship with UserLearningProgress
  resourceRatings  ResourceRating[] // One-to-many relationship with ResourceRating
  goals         UserGoal[] // One-to-many relationship with UserGoal
  achievements  UserAchievement[] // One-to-many relationship with UserAchievement

  // Added for NextAuth
  accounts Account[]
  sessions Session[]

  @@index([email])
  @@index([createdAt])
  @@index([passwordResetToken])
}

model Profile {
  id                String  @id @default(uuid())
  userId            String  @unique // One-to-one relationship with User
  user              User    @relation(fields: [userId], references: [id])
  bio               String?
  profilePictureUrl String?
  socialMediaLinks  Json?

  // Phase 2 Enhanced Profile Fields
  firstName         String?
  lastName          String?
  jobTitle          String?
  company           String?
  location          String?
  phoneNumber       String?
  website           String?

  // Career-related fields
  careerInterests   Json? // Array of career interests
  skillsToLearn     Json? // Array of skills user wants to learn
  experienceLevel   ExperienceLevel? @default(BEGINNER)
  currentIndustry   String?
  targetIndustry    String?

  // Profile completion tracking
  profileCompletionScore Int @default(0) // 0-100 percentage
  lastProfileUpdate DateTime @default(now())

  // Resume upload
  resumeUrl         String?
  resumeFileName    String?
  resumeUploadedAt  DateTime?

  // Preferences
  weeklyLearningGoal Int @default(3) // Number of resources per week
  emailNotifications Boolean @default(true)
  profileVisibility ProfileVisibility @default(PRIVATE)

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

enum ExperienceLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum ProfileVisibility {
  PRIVATE
  PUBLIC
  COMMUNITY_ONLY
}

enum AssessmentStatus {
  IN_PROGRESS
  COMPLETED
}

model Assessment {
  id          String               @id @default(uuid())
  userId      String
  user        User                 @relation(fields: [userId], references: [id])
  status      AssessmentStatus     @default(IN_PROGRESS)
  currentStep Int                  @default(0)
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt
  completedAt DateTime?
  responses   AssessmentResponse[]

  @@index([userId])
  @@index([status])
  @@index([completedAt])
}

model AssessmentResponse {
  id           String     @id @default(uuid())
  assessmentId String
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  questionKey  String // e.g., "dissatisfaction_triggers", "desired_outcomes_skill_a"
  answerValue  Json // Flexible to store various answer types (string, array, number)
  createdAt    DateTime   @default(now())

  @@index([assessmentId])
}

model CareerPath {
  id                String           @id @default(uuid())
  name              String           @unique
  slug              String           @unique // For URL-friendly identifiers
  overview          String
  pros              String // JSON string of advantages
  cons              String // JSON string of disadvantages
  actionableSteps   Json // e.g., [{ title: "Step 1", description: "..."}]
  isActive          Boolean          @default(true)
  relatedSkills     Skill[]          @relation("CareerPathToSkill")
  relatedIndustries Industry[]       @relation("CareerPathToIndustry")
  suggestionRules   SuggestionRule[]
  learningResources LearningResource[] @relation("CareerPathToLearningResource")
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
}

model Skill {
  id                String             @id @default(uuid())
  name              String             @unique
  description       String?
  category          String? // e.g., "Technical", "Soft Skills", "Industry-Specific"
  careerPaths       CareerPath[]       @relation("CareerPathToSkill")
  learningResources LearningResource[] @relation("SkillToLearningResource")
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
}

model Industry {
  id          String       @id @default(uuid())
  name        String       @unique
  careerPaths CareerPath[] @relation("CareerPathToIndustry")
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

model SuggestionRule {
  id           String     @id @default(uuid())
  careerPathId String
  careerPath   CareerPath @relation(fields: [careerPathId], references: [id])
  questionKey  String // Corresponds to AssessmentResponse.questionKey
  answerValue  Json // The specific answer value or pattern that triggers this rule
  weight       Float      @default(1.0) // How much this rule contributes
  notes        String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  @@index([careerPathId])
}

model ForumPost {
  id        String       @id @default(uuid())
  title     String
  content   String
  authorId  String
  author    User         @relation(fields: [authorId], references: [id])
  replies   ForumReply[] // One-to-many relationship with ForumReply
  reactions ForumReaction[] // One-to-many relationship with ForumReaction
  category  String?      // Optional category for better organization
  tags      Json?        // Array of tags for better searchability
  isPinned  Boolean      @default(false) // For moderator pinning
  isLocked  Boolean      @default(false) // For moderator locking
  viewCount Int          @default(0) // Track post views
  createdAt DateTime     @default(now())
  updatedAt DateTime     @default(now()) @updatedAt
}

model ForumReply {
  id        String    @id @default(uuid())
  content   String
  authorId  String
  author    User      @relation(fields: [authorId], references: [id])
  postId    String
  post      ForumPost @relation(fields: [postId], references: [id])
  reactions ForumReaction[] // One-to-many relationship with ForumReaction
  createdAt DateTime  @default(now())
  updatedAt DateTime  @default(now()) @updatedAt
}

model ForumReaction {
  id        String      @id @default(uuid())
  userId    String
  user      User        @relation(fields: [userId], references: [id])
  postId    String?
  post      ForumPost?  @relation(fields: [postId], references: [id])
  replyId   String?
  reply     ForumReply? @relation(fields: [replyId], references: [id])
  type      ReactionType
  createdAt DateTime    @default(now())

  @@unique([userId, postId])
  @@unique([userId, replyId])
}

model FreedomFund {
  id                   String   @id @default(uuid())
  userId               String   @unique
  user                 User     @relation(fields: [userId], references: [id])
  monthlyExpenses      Float
  coverageMonths       Int
  targetSavings        Float
  currentSavingsAmount Float? // Optional, as user might not have started saving
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt // Automatically updated on save
}

// Added for NextAuth
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

// Added for NextAuth
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// Added for NextAuth
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// New models for Learning Resources
model LearningResource {
  id                String                    @id @default(uuid())
  title             String
  description       String
  url               String                    @unique
  type              LearningResourceType
  category          LearningResourceCategory
  skillLevel        SkillLevel
  author            String?
  duration          String?
  cost              LearningResourceCost      @default(FREE)
  format            LearningResourceFormat
  isActive          Boolean                   @default(true)
  careerPaths       CareerPath[]              @relation("CareerPathToLearningResource")
  skills            Skill[]                   @relation("SkillToLearningResource")
  userProgress      UserLearningProgress[]
  ratings           ResourceRating[]
  createdAt         DateTime                  @default(now())
  updatedAt         DateTime                  @updatedAt

  @@index([category])
  @@index([skillLevel])
  @@index([type])
  @@index([cost])
  @@index([isActive])
  @@index([createdAt])
}

model UserLearningProgress {
  id                String           @id @default(uuid())
  userId            String
  user              User             @relation(fields: [userId], references: [id])
  resourceId        String
  resource          LearningResource @relation(fields: [resourceId], references: [id])
  status            ProgressStatus   @default(NOT_STARTED)
  completedAt       DateTime?
  notes             String?
  rating            Int?             // 1-5 star rating
  review            String?          // User review text
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  @@unique([userId, resourceId])
}

model ResourceRating {
  id                String           @id @default(uuid())
  userId            String
  user              User             @relation(fields: [userId], references: [id])
  resourceId        String
  resource          LearningResource @relation(fields: [resourceId], references: [id])
  rating            Int              // 1-5 star rating
  review            String?          // Optional review text
  isHelpful         Boolean?         // Whether user found resource helpful
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  @@unique([userId, resourceId])
}

enum LearningResourceType {
  COURSE
  ARTICLE
  VIDEO
  PODCAST
  BOOK
  CERTIFICATION
  TUTORIAL
  WORKSHOP
}

enum LearningResourceCategory {
  CYBERSECURITY
  DATA_SCIENCE
  BLOCKCHAIN
  PROJECT_MANAGEMENT
  DIGITAL_MARKETING
  FINANCIAL_LITERACY
  LANGUAGE_LEARNING
  ARTIFICIAL_INTELLIGENCE
  WEB_DEVELOPMENT
  MOBILE_DEVELOPMENT
  CLOUD_COMPUTING
  ENTREPRENEURSHIP
}

enum SkillLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum LearningResourceCost {
  FREE
  FREEMIUM
  PAID
  SUBSCRIPTION
}

enum LearningResourceFormat {
  SELF_PACED
  INSTRUCTOR_LED
  INTERACTIVE
  HANDS_ON
  THEORETICAL
}

enum ProgressStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  BOOKMARKED
}

enum ReactionType {
  LIKE
  DISLIKE
  HELPFUL
  INSIGHTFUL
  INSPIRING
}

enum GoalType {
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
  CUSTOM
}

enum GoalCategory {
  LEARNING_RESOURCES
  SKILLS
  CERTIFICATIONS
  PROJECTS
  CAREER_MILESTONES
  NETWORKING
}

enum GoalStatus {
  ACTIVE
  COMPLETED
  PAUSED
  CANCELLED
}

enum AchievementType {
  LEARNING_MILESTONE
  STREAK_ACHIEVEMENT
  COMPLETION_BADGE
  COMMUNITY_CONTRIBUTOR
  SKILL_MASTER
  GOAL_ACHIEVER
}

// User Goals System
model UserGoal {
  id          String      @id @default(uuid())
  userId      String
  user        User        @relation(fields: [userId], references: [id])
  title       String
  description String?
  type        GoalType
  category    GoalCategory
  status      GoalStatus  @default(ACTIVE)
  targetValue Int         // e.g., number of resources to complete
  currentValue Int        @default(0) // current progress
  startDate   DateTime    @default(now())
  targetDate  DateTime?   // optional deadline
  completedAt DateTime?
  isPublic    Boolean     @default(false) // whether goal is visible to community
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  @@index([userId, status])
}

// Achievement System
model Achievement {
  id          String            @id @default(uuid())
  name        String            @unique
  description String
  type        AchievementType
  icon        String            // icon identifier or URL
  criteria    Json              // achievement criteria (flexible JSON)
  points      Int               @default(0) // points awarded
  isActive    Boolean           @default(true)
  userAchievements UserAchievement[]
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
}

model UserAchievement {
  id            String      @id @default(uuid())
  userId        String
  user          User        @relation(fields: [userId], references: [id])
  achievementId String
  achievement   Achievement @relation(fields: [achievementId], references: [id])
  unlockedAt    DateTime    @default(now())
  progress      Json?       // track progress towards achievement

  @@unique([userId, achievementId])
  @@index([userId])
}
