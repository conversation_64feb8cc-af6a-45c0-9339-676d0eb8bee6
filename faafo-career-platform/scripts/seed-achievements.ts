import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedAchievements() {
  console.log('🌱 Seeding achievements...');

  const achievements = [
    {
      name: 'First Steps',
      description: 'Complete your first learning resource',
      type: 'LEARNING_MILESTONE',
      icon: 'achievement-learning_milestone',
      criteria: { completedResources: 1 },
      points: 10,
    },
    {
      name: 'Learning Enthusiast',
      description: 'Complete 10 learning resources',
      type: 'LEARNING_MILESTONE',
      icon: 'achievement-learning_milestone',
      criteria: { completedResources: 10 },
      points: 50,
    },
    {
      name: 'Knowledge Seeker',
      description: 'Complete 25 learning resources',
      type: 'LEARNING_MILESTONE',
      icon: 'achievement-learning_milestone',
      criteria: { completedResources: 25 },
      points: 100,
    },
    {
      name: 'Master Learner',
      description: 'Complete 50 learning resources',
      type: 'LEARNING_MILESTONE',
      icon: 'achievement-learning_milestone',
      criteria: { completedResources: 50 },
      points: 200,
    },
    {
      name: 'Community Helper',
      description: 'Make 5 forum posts or replies',
      type: 'COMMUNITY_CONTRIBUTOR',
      icon: 'achievement-community_contributor',
      criteria: { forumActivity: 5 },
      points: 25,
    },
    {
      name: 'Forum Regular',
      description: 'Make 20 forum posts or replies',
      type: 'COMMUNITY_CONTRIBUTOR',
      icon: 'achievement-community_contributor',
      criteria: { forumActivity: 20 },
      points: 75,
    },
    {
      name: 'Community Leader',
      description: 'Make 50 forum posts or replies',
      type: 'COMMUNITY_CONTRIBUTOR',
      icon: 'achievement-community_contributor',
      criteria: { forumActivity: 50 },
      points: 150,
    },
    {
      name: 'Goal Achiever',
      description: 'Complete your first goal',
      type: 'GOAL_ACHIEVER',
      icon: 'achievement-goal_achiever',
      criteria: { completedGoals: 1 },
      points: 20,
    },
    {
      name: 'Goal Master',
      description: 'Complete 5 goals',
      type: 'GOAL_ACHIEVER',
      icon: 'achievement-goal_achiever',
      criteria: { completedGoals: 5 },
      points: 75,
    },
    {
      name: 'Dedicated Learner',
      description: 'Maintain a 7-day learning streak',
      type: 'STREAK_ACHIEVEMENT',
      icon: 'achievement-streak_achievement',
      criteria: { streakDays: 7 },
      points: 30,
    },
    {
      name: 'Consistency Champion',
      description: 'Maintain a 30-day learning streak',
      type: 'STREAK_ACHIEVEMENT',
      icon: 'achievement-streak_achievement',
      criteria: { streakDays: 30 },
      points: 100,
    },
    {
      name: 'Reviewer',
      description: 'Rate 5 learning resources',
      type: 'COMPLETION_BADGE',
      icon: 'achievement-completion_badge',
      criteria: { ratings: 5 },
      points: 15,
    },
    {
      name: 'Critic',
      description: 'Rate 20 learning resources',
      type: 'COMPLETION_BADGE',
      icon: 'achievement-completion_badge',
      criteria: { ratings: 20 },
      points: 50,
    },
    {
      name: 'Skill Explorer',
      description: 'Complete resources in 3 different categories',
      type: 'SKILL_MASTER',
      icon: 'achievement-skill_master',
      criteria: { categoriesExplored: 3 },
      points: 40,
    },
    {
      name: 'Renaissance Learner',
      description: 'Complete resources in 6 different categories',
      type: 'SKILL_MASTER',
      icon: 'achievement-skill_master',
      criteria: { categoriesExplored: 6 },
      points: 100,
    },
  ];

  for (const achievement of achievements) {
    try {
      await prisma.achievement.upsert({
        where: { name: achievement.name },
        update: achievement,
        create: achievement,
      });
      console.log(`✅ Created/updated achievement: ${achievement.name}`);
    } catch (error) {
      console.error(`❌ Error creating achievement ${achievement.name}:`, error);
    }
  }

  console.log('🎉 Achievement seeding completed!');
}

async function main() {
  try {
    await seedAchievements();
  } catch (error) {
    console.error('Error seeding achievements:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

export default main;
