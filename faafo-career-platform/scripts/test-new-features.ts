import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testNewFeatures() {
  console.log('🧪 Testing new Community Forum and Progress Tracking features...\n');

  try {
    // Test 1: Check if new database models exist
    console.log('1. Testing database schema...');
    
    // Test ForumReaction model
    const reactionCount = await prisma.forumReaction.count();
    console.log(`   ✅ ForumReaction model exists (${reactionCount} records)`);
    
    // Test UserGoal model
    const goalCount = await prisma.userGoal.count();
    console.log(`   ✅ UserGoal model exists (${goalCount} records)`);
    
    // Test Achievement model
    const achievementCount = await prisma.achievement.count();
    console.log(`   ✅ Achievement model exists (${achievementCount} records)`);
    
    // Test UserAchievement model
    const userAchievementCount = await prisma.userAchievement.count();
    console.log(`   ✅ UserAchievement model exists (${userAchievementCount} records)`);

    // Test 2: Check if forum posts have new fields
    console.log('\n2. Testing enhanced forum posts...');
    const forumPost = await prisma.forumPost.findFirst({
      select: {
        id: true,
        title: true,
        category: true,
        tags: true,
        isPinned: true,
        isLocked: true,
        viewCount: true,
        updatedAt: true,
      },
    });
    
    if (forumPost) {
      console.log(`   ✅ Forum post enhanced fields available`);
      console.log(`      - Category: ${forumPost.category || 'null'}`);
      console.log(`      - Tags: ${forumPost.tags ? JSON.stringify(forumPost.tags) : 'null'}`);
      console.log(`      - View count: ${forumPost.viewCount}`);
      console.log(`      - Is pinned: ${forumPost.isPinned}`);
      console.log(`      - Is locked: ${forumPost.isLocked}`);
    } else {
      console.log(`   ⚠️  No forum posts found to test enhanced fields`);
    }

    // Test 3: Create a sample goal
    console.log('\n3. Testing goal creation...');
    
    // Find a user to test with
    const testUser = await prisma.user.findFirst();
    if (testUser) {
      const sampleGoal = await prisma.userGoal.create({
        data: {
          userId: testUser.id,
          title: 'Complete 5 Learning Resources',
          description: 'Test goal for the new goal system',
          type: 'WEEKLY',
          category: 'LEARNING_RESOURCES',
          targetValue: 5,
          currentValue: 2,
          isPublic: false,
        },
      });
      console.log(`   ✅ Sample goal created: ${sampleGoal.title}`);
      console.log(`      - Progress: ${sampleGoal.currentValue}/${sampleGoal.targetValue}`);
      console.log(`      - Type: ${sampleGoal.type}`);
      console.log(`      - Category: ${sampleGoal.category}`);
    } else {
      console.log(`   ⚠️  No users found to create test goal`);
    }

    // Test 4: Test achievement unlocking
    console.log('\n4. Testing achievement system...');
    
    if (testUser) {
      // Check if user has any achievements
      const userAchievements = await prisma.userAchievement.findMany({
        where: { userId: testUser.id },
        include: { achievement: true },
      });
      
      console.log(`   ✅ User has ${userAchievements.length} achievements unlocked`);
      
      if (userAchievements.length > 0) {
        userAchievements.forEach((ua, index) => {
          console.log(`      ${index + 1}. ${ua.achievement.name} (${ua.achievement.points} points)`);
        });
      }

      // Try to unlock a sample achievement
      const firstStepsAchievement = await prisma.achievement.findUnique({
        where: { name: 'First Steps' },
      });

      if (firstStepsAchievement) {
        const existingUserAchievement = await prisma.userAchievement.findUnique({
          where: {
            userId_achievementId: {
              userId: testUser.id,
              achievementId: firstStepsAchievement.id,
            },
          },
        });

        if (!existingUserAchievement) {
          const newUserAchievement = await prisma.userAchievement.create({
            data: {
              userId: testUser.id,
              achievementId: firstStepsAchievement.id,
            },
            include: { achievement: true },
          });
          console.log(`   ✅ Unlocked achievement: ${newUserAchievement.achievement.name}`);
        } else {
          console.log(`   ℹ️  User already has "First Steps" achievement`);
        }
      }
    }

    // Test 5: Test forum reactions
    console.log('\n5. Testing forum reactions...');
    
    const forumPostForReaction = await prisma.forumPost.findFirst();
    if (forumPostForReaction && testUser) {
      // Create a sample reaction
      const sampleReaction = await prisma.forumReaction.create({
        data: {
          userId: testUser.id,
          postId: forumPostForReaction.id,
          type: 'LIKE',
        },
        include: {
          user: { select: { id: true, name: true, email: true } },
          post: { select: { id: true, title: true } },
        },
      });
      
      console.log(`   ✅ Sample reaction created: ${sampleReaction.type}`);
      console.log(`      - User: ${sampleReaction.user.name || sampleReaction.user.email}`);
      console.log(`      - Post: ${sampleReaction.post.title}`);
    } else {
      console.log(`   ⚠️  No forum posts or users found to test reactions`);
    }

    // Test 6: Check enhanced user profiles
    console.log('\n6. Testing enhanced user profiles...');
    
    const userWithProfile = await prisma.user.findFirst({
      include: {
        profile: true,
        _count: {
          select: {
            forumPosts: true,
            forumReplies: true,
            achievements: true,
            goals: true,
          },
        },
      },
    });

    if (userWithProfile) {
      console.log(`   ✅ User profile data available`);
      console.log(`      - Forum posts: ${userWithProfile._count.forumPosts}`);
      console.log(`      - Forum replies: ${userWithProfile._count.forumReplies}`);
      console.log(`      - Achievements: ${userWithProfile._count.achievements}`);
      console.log(`      - Goals: ${userWithProfile._count.goals}`);
      
      if (userWithProfile.profile) {
        console.log(`      - Profile completion: ${userWithProfile.profile.profileCompletionScore}%`);
        console.log(`      - Experience level: ${userWithProfile.profile.experienceLevel}`);
      }
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary of new features:');
    console.log('   ✅ Enhanced forum posts with categories, tags, and view counts');
    console.log('   ✅ Forum reaction system (like, dislike, helpful, insightful, inspiring)');
    console.log('   ✅ User profile cards in forum posts and replies');
    console.log('   ✅ Goal setting and tracking system');
    console.log('   ✅ Achievement system with multiple types');
    console.log('   ✅ Enhanced user profiles with stats and completion tracking');
    console.log('   ✅ Progress dashboard with tabs for overview, goals, achievements, and learning');

  } catch (error) {
    console.error('❌ Error during testing:', error);
  }
}

async function main() {
  try {
    await testNewFeatures();
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

export default main;
