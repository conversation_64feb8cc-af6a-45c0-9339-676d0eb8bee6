'use client'; // Required for useSession hook

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useSession, signIn, signOut } from 'next-auth/react';
import { useTheme } from 'next-themes';
import { usePathname } from 'next/navigation';
import {
  Compass,
  HomeIcon,
  User,
  LogOut,
  LogIn,
  Moon,
  UserPlus,
  Sun,
  HelpCircle,
  MessageSquare,
  Briefcase,
  BookOpen,
  LayoutDashboard,
  GraduationCap,
  DollarSign,
  TrendingUp,
  Target,
  Menu,
  X,
  ChevronDown
} from 'lucide-react';

interface NavLinkProps {
  href: string;
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  isActive?: boolean;
}

const NavLink: React.FC<NavLinkProps> = ({ href, children, onClick, className = '', isActive = false }) => {
  const baseClasses = "inline-flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200";
  const activeClasses = isActive
    ? "text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/20"
    : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700";

  return (
    <Link
      href={href}
      onClick={onClick}
      className={`${baseClasses} ${activeClasses} ${className}`}
    >
      {children}
    </Link>
  );
};

export function NavigationBar() {
  const { status } = useSession();
  const isAuthenticated = status === 'authenticated';
  const { theme, setTheme } = useTheme();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isToolsDropdownOpen, setIsToolsDropdownOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const mobileMenuRef = useRef<HTMLDivElement>(null);
  const toolsDropdownRef = useRef<HTMLDivElement>(null);

  // Set mounted state after component mounts
  useEffect(() => {
    setMounted(true);
  }, []);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
        setIsMobileMenuOpen(false);
      }
      if (toolsDropdownRef.current && !toolsDropdownRef.current.contains(event.target as Node)) {
        setIsToolsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Close mobile menu on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsMobileMenuOpen(false);
        setIsToolsDropdownOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
    setIsToolsDropdownOpen(false);
  };

  const toggleToolsDropdown = () => {
    setIsToolsDropdownOpen(!isToolsDropdownOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <header className="sticky top-0 z-50 bg-background/95 dark:bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" aria-label="Main navigation">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center text-gray-900 dark:text-gray-100 hover:opacity-80 transition-opacity">
              <Compass className="h-8 w-8 mr-2 text-indigo-600 dark:text-indigo-400" />
              <span className="text-2xl font-bold">FAAFO</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            <NavLink href="/" isActive={pathname === '/'}>
              <HomeIcon className="h-4 w-4" />
              <span>Home</span>
            </NavLink>

            {isAuthenticated && (
              <NavLink href="/dashboard" isActive={pathname === '/dashboard'}>
                <LayoutDashboard className="h-4 w-4" />
                <span>Dashboard</span>
              </NavLink>
            )}

            <NavLink href="/career-paths" isActive={pathname.startsWith('/career-paths')}>
              <Briefcase className="h-4 w-4" />
              <span>Career Paths</span>
            </NavLink>

            <NavLink href="/resources" isActive={pathname.startsWith('/resources')}>
              <BookOpen className="h-4 w-4" />
              <span>Resources</span>
            </NavLink>

            {/* Tools Dropdown for authenticated users */}
            {isAuthenticated && (
              <div className="relative" ref={toolsDropdownRef}>
                <button
                  onClick={toggleToolsDropdown}
                  className="inline-flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                  aria-expanded={isToolsDropdownOpen}
                  aria-haspopup="true"
                >
                  <Target className="h-4 w-4" />
                  <span>Tools</span>
                  <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${isToolsDropdownOpen ? 'rotate-180' : ''}`} />
                </button>

                {isToolsDropdownOpen && (
                  <div className="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                    <div className="py-1">
                      <Link
                        href="/assessment"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        onClick={() => setIsToolsDropdownOpen(false)}
                      >
                        <GraduationCap className="h-4 w-4 mr-3" />
                        Career Assessment
                      </Link>
                      <Link
                        href="/freedom-fund"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        onClick={() => setIsToolsDropdownOpen(false)}
                      >
                        <DollarSign className="h-4 w-4 mr-3" />
                        Freedom Fund Calculator
                      </Link>
                      <Link
                        href="/progress"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        onClick={() => setIsToolsDropdownOpen(false)}
                      >
                        <TrendingUp className="h-4 w-4 mr-3" />
                        Progress Tracker
                      </Link>
                      <Link
                        href="/recommendations"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        onClick={() => setIsToolsDropdownOpen(false)}
                      >
                        <Target className="h-4 w-4 mr-3" />
                        Recommendations
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            )}

            {isAuthenticated && (
              <NavLink href="/forum" isActive={pathname.startsWith('/forum')}>
                <MessageSquare className="h-4 w-4" />
                <span>Forum</span>
              </NavLink>
            )}

            <NavLink href="/help" isActive={pathname.startsWith('/help')}>
              <HelpCircle className="h-4 w-4" />
              <span>Help</span>
            </NavLink>
          </div>

          {/* Right side - Auth & Theme */}
          <div className="hidden lg:flex items-center space-x-2">
            {isAuthenticated ? (
              <>
                <NavLink href="/profile" isActive={pathname.startsWith('/profile')}>
                  <User className="h-4 w-4" />
                  <span>Profile</span>
                </NavLink>
                <button
                  onClick={() => signOut()}
                  className="inline-flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200"
                  aria-label="Sign out"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Sign Out</span>
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => signIn()}
                  className="inline-flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                  aria-label="Log in"
                >
                  <LogIn className="h-4 w-4" />
                  <span>Log In</span>
                </button>
                <Link
                  href="/signup"
                  className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-400 transition-colors duration-200"
                >
                  <UserPlus className="h-4 w-4 mr-1" />
                  Sign Up
                </Link>
              </>
            )}

            {/* Theme Toggle */}
            <button
              aria-label="Toggle theme"
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            >
              {!mounted ? (
                <Sun className="h-5 w-5" />
              ) : theme === 'dark' ? (
                <Sun className="h-5 w-5" />
              ) : (
                <Moon className="h-5 w-5" />
              )}
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden flex items-center space-x-2">
            {/* Mobile Theme Toggle */}
            <button
              aria-label="Toggle theme"
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            >
              {!mounted ? (
                <Sun className="h-5 w-5" />
              ) : theme === 'dark' ? (
                <Sun className="h-5 w-5" />
              ) : (
                <Moon className="h-5 w-5" />
              )}
            </button>

            {/* Mobile Menu Toggle */}
            <button
              onClick={toggleMobileMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
              aria-expanded={isMobileMenuOpen}
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div
            ref={mobileMenuRef}
            className="lg:hidden border-t border-border bg-background dark:bg-card"
          >
            <div className="px-2 pt-2 pb-3 space-y-1">
              <MobileNavLink href="/" isActive={pathname === '/'} onClick={closeMobileMenu}>
                <HomeIcon className="h-5 w-5" />
                <span>Home</span>
              </MobileNavLink>

              {isAuthenticated && (
                <MobileNavLink href="/dashboard" isActive={pathname === '/dashboard'} onClick={closeMobileMenu}>
                  <LayoutDashboard className="h-5 w-5" />
                  <span>Dashboard</span>
                </MobileNavLink>
              )}

              <MobileNavLink href="/career-paths" isActive={pathname.startsWith('/career-paths')} onClick={closeMobileMenu}>
                <Briefcase className="h-5 w-5" />
                <span>Career Paths</span>
              </MobileNavLink>

              <MobileNavLink href="/resources" isActive={pathname.startsWith('/resources')} onClick={closeMobileMenu}>
                <BookOpen className="h-5 w-5" />
                <span>Resources</span>
              </MobileNavLink>

              {isAuthenticated && (
                <>
                  <div className="pt-2 pb-1">
                    <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider px-3 py-1">
                      Tools
                    </div>
                  </div>

                  <MobileNavLink href="/assessment" isActive={pathname.startsWith('/assessment')} onClick={closeMobileMenu}>
                    <GraduationCap className="h-5 w-5" />
                    <span>Career Assessment</span>
                  </MobileNavLink>

                  <MobileNavLink href="/freedom-fund" isActive={pathname.startsWith('/freedom-fund')} onClick={closeMobileMenu}>
                    <DollarSign className="h-5 w-5" />
                    <span>Freedom Fund Calculator</span>
                  </MobileNavLink>

                  <MobileNavLink href="/progress" isActive={pathname.startsWith('/progress')} onClick={closeMobileMenu}>
                    <TrendingUp className="h-5 w-5" />
                    <span>Progress Tracker</span>
                  </MobileNavLink>

                  <MobileNavLink href="/recommendations" isActive={pathname.startsWith('/recommendations')} onClick={closeMobileMenu}>
                    <Target className="h-5 w-5" />
                    <span>Recommendations</span>
                  </MobileNavLink>

                  <MobileNavLink href="/forum" isActive={pathname.startsWith('/forum')} onClick={closeMobileMenu}>
                    <MessageSquare className="h-5 w-5" />
                    <span>Forum</span>
                  </MobileNavLink>
                </>
              )}

              <MobileNavLink href="/help" isActive={pathname.startsWith('/help')} onClick={closeMobileMenu}>
                <HelpCircle className="h-5 w-5" />
                <span>Help</span>
              </MobileNavLink>

              {/* Mobile Auth Section */}
              <div className="pt-4 border-t border-border">
                {isAuthenticated ? (
                  <>
                    <MobileNavLink href="/profile" isActive={pathname.startsWith('/profile')} onClick={closeMobileMenu}>
                      <User className="h-5 w-5" />
                      <span>Profile</span>
                    </MobileNavLink>
                    <button
                      onClick={() => {
                        signOut();
                        closeMobileMenu();
                      }}
                      className="w-full flex items-center space-x-3 px-3 py-2 rounded-md text-base font-medium text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200"
                    >
                      <LogOut className="h-5 w-5" />
                      <span>Sign Out</span>
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => {
                        signIn();
                        closeMobileMenu();
                      }}
                      className="w-full flex items-center space-x-3 px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                    >
                      <LogIn className="h-5 w-5" />
                      <span>Log In</span>
                    </button>
                    <Link
                      href="/signup"
                      onClick={closeMobileMenu}
                      className="w-full flex items-center space-x-3 px-3 py-2 mt-2 rounded-md text-base font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-400 transition-colors duration-200"
                    >
                      <UserPlus className="h-5 w-5" />
                      <span>Sign Up</span>
                    </Link>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
}

// Mobile Navigation Link Component
interface MobileNavLinkProps {
  href: string;
  children: React.ReactNode;
  onClick?: () => void;
  isActive?: boolean;
}

const MobileNavLink: React.FC<MobileNavLinkProps> = ({ href, children, onClick, isActive = false }) => {
  const baseClasses = "flex items-center space-x-3 px-3 py-2 rounded-md text-base font-medium transition-colors duration-200";
  const activeClasses = isActive
    ? "text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/20"
    : "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700";

  return (
    <Link
      href={href}
      onClick={onClick}
      className={`${baseClasses} ${activeClasses}`}
    >
      {children}
    </Link>
  );
};