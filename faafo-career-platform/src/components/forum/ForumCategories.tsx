'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { MessageSquare, Users, Clock, ChevronRight, Pin } from 'lucide-react';

interface ForumCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  color?: string;
  postCount: number;
  replyCount: number;
  lastPost?: {
    id: string;
    title: string;
    author: {
      id: string;
      name?: string;
      email: string;
    };
    createdAt: string;
  };
  children: ForumCategory[];
}

interface ForumCategoriesProps {
  onCategorySelect?: (categoryId: string) => void;
  selectedCategoryId?: string;
}

export default function ForumCategories({ onCategorySelect, selectedCategoryId }: ForumCategoriesProps) {
  const [categories, setCategories] = useState<ForumCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/forum/categories');
      
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }

      const data = await response.json();
      setCategories(data.data || data);
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError(err instanceof Error ? err.message : 'Failed to load categories');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else if (diffInHours < 168) {
      return `${Math.floor(diffInHours / 24)}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const CategoryItem = ({ category, isSubcategory = false }: { category: ForumCategory; isSubcategory?: boolean }) => {
    const isSelected = selectedCategoryId === category.id;
    
    return (
      <div 
        className={`
          border rounded-lg p-4 transition-all duration-200 cursor-pointer
          ${isSelected 
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
          }
          ${isSubcategory ? 'ml-6 border-l-4' : ''}
        `}
        onClick={() => onCategorySelect?.(category.id)}
        style={isSubcategory && category.color ? { borderLeftColor: category.color } : {}}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              {category.icon && (
                <span className="text-2xl" role="img" aria-label={category.name}>
                  {category.icon}
                </span>
              )}
              <div>
                <h3 className={`font-semibold ${isSubcategory ? 'text-base' : 'text-lg'} text-gray-900 dark:text-white`}>
                  {category.name}
                </h3>
                {category.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {category.description}
                  </p>
                )}
              </div>
            </div>

            {/* Statistics */}
            <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <MessageSquare className="h-4 w-4" />
                <span>{category.postCount} posts</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span>{category.replyCount} replies</span>
              </div>
            </div>
          </div>

          {/* Last Post Info */}
          {category.lastPost && (
            <div className="text-right text-sm">
              <div className="text-gray-900 dark:text-white font-medium truncate max-w-48">
                {category.lastPost.title}
              </div>
              <div className="text-gray-500 dark:text-gray-400">
                by {category.lastPost.author.name || category.lastPost.author.email}
              </div>
              <div className="flex items-center gap-1 text-gray-400 dark:text-gray-500 justify-end">
                <Clock className="h-3 w-3" />
                <span>{formatDate(category.lastPost.createdAt)}</span>
              </div>
            </div>
          )}

          {!isSubcategory && category.children.length > 0 && (
            <ChevronRight className="h-5 w-5 text-gray-400 ml-2" />
          )}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 animate-pulse">
            <div className="flex items-center gap-3 mb-2">
              <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
              <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-32"></div>
            </div>
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-full mb-2"></div>
            <div className="flex gap-4">
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-20"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-20"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
        <button
          onClick={fetchCategories}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Forum Categories</h2>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {categories.reduce((total, cat) => total + cat.postCount, 0)} total posts
        </div>
      </div>

      {categories.map((category) => (
        <div key={category.id} className="space-y-2">
          <CategoryItem category={category} />
          
          {/* Subcategories */}
          {category.children.map((subcategory) => (
            <CategoryItem 
              key={subcategory.id} 
              category={subcategory} 
              isSubcategory={true} 
            />
          ))}
        </div>
      ))}

      {categories.length === 0 && (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No categories available yet.</p>
        </div>
      )}
    </div>
  );
}
