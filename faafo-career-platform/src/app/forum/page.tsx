'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { MessageSquare, Plus, User, Calendar, Filter, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ForumCategories from '@/components/forum/ForumCategories';
import ForumUserProfile from '@/components/forum/ForumUserProfile';
import ReactionButtons from '@/components/forum/ReactionButtons';
import BookmarkButton from '@/components/forum/BookmarkButton';
import ReportButton from '@/components/forum/ReportButton';

interface ForumPost {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  isPinned: boolean;
  viewCount: number;
  likeCount: number;
  author: {
    id: string;
    email: string;
    name?: string;
    profile?: {
      profilePictureUrl?: string;
      forumReputation?: number;
      forumPostCount?: number;
      currentCareerPath?: string;
      progressLevel?: string;
    };
  };
  category?: {
    id: string;
    name: string;
    slug: string;
  };
  _count: {
    replies: number;
    reactions: number;
  };
  reactions: Array<{
    type: string;
    userId: string;
  }>;
}

export default function ForumPage() {
  const { status } = useSession();
  const router = useRouter();
  const [posts, setPosts] = useState<ForumPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCategories, setShowCategories] = useState(true);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    if (status === 'authenticated') {
      fetchPosts();
    }
  }, [status, router, selectedCategoryId]);

  const fetchPosts = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedCategoryId) {
        params.append('categoryId', selectedCategoryId);
      }

      const response = await fetch(`/api/forum/posts?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch posts');
      }

      const data = await response.json();
      setPosts(data.posts || data); // Handle both paginated and non-paginated responses
    } catch (err) {
      console.error('Error fetching posts:', err);
      setError('Failed to load forum posts');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDisplayName = (author: { name?: string; email: string }) => {
    return author.name || author.email.split('@')[0];
  };

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading forum...</div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p>Please <Link href="/login" className="text-blue-600 hover:underline">log in</Link> to access the forum.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">Community Forum</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Connect with others on their career transition journey
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => setShowCategories(!showCategories)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            {showCategories ? 'Hide Categories' : 'Show Categories'}
          </Button>
          <Button asChild>
            <Link href="/forum/new" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              New Post
            </Link>
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Categories Sidebar */}
        {showCategories && (
          <div className="lg:col-span-1">
            <ForumCategories
              onCategorySelect={setSelectedCategoryId}
              selectedCategoryId={selectedCategoryId || undefined}
            />
          </div>
        )}

        {/* Main Content */}
        <div className={showCategories ? "lg:col-span-3" : "lg:col-span-4"}>

          <div className="space-y-6">
            {posts.length === 0 ? (
              <div className="text-center py-12">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  No posts yet
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Be the first to start a conversation in the community!
                </p>
                <Button asChild>
                  <Link href="/forum/new">Create First Post</Link>
                </Button>
              </div>
            ) : (
              posts.map((post) => (
                <div
                  key={post.id}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow"
                >
                  {/* Post Header */}
                  <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {post.isPinned && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                              📌 Pinned
                            </span>
                          )}
                          {post.category && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                              {post.category.name}
                            </span>
                          )}
                        </div>
                        <Link
                          href={`/forum/posts/${post.id}`}
                          className="text-xl font-semibold text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 block"
                        >
                          {post.title}
                        </Link>
                      </div>
                    </div>

                    {/* Author Info */}
                    <ForumUserProfile
                      user={post.author}
                      joinDate={post.createdAt}
                      size="medium"
                    />
                  </div>

                  {/* Post Content */}
                  <div className="p-6">
                    <p className="text-gray-700 dark:text-gray-300 line-clamp-3 mb-4">
                      {post.content.length > 200
                        ? `${post.content.substring(0, 200)}...`
                        : post.content
                      }
                    </p>

                    {/* Post Actions */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <ReactionButtons
                          postId={post.id}
                          initialReactions={[]}
                          size="small"
                        />
                        <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                          <MessageSquare className="h-4 w-4" />
                          <span>{post._count.replies} replies</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(post.createdAt)}</span>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <BookmarkButton
                          postId={post.id}
                          size="small"
                        />
                        <ReportButton
                          postId={post.id}
                          size="small"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
