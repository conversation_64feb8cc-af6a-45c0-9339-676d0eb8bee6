import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import SessionWrapper from '../../components/SessionWrapper';
import CookieConsentBanner from "../../components/CookieConsentBanner";
import { ThemeProvider } from "@/components/layout/ThemeProvider";
import VercelAnalyticsWrapper from "./components/layout/VercelAnalyticsWrapper";
import Footer from "@/components/layout/Footer";
import { ErrorBoundary } from "@/components/ErrorBoundary";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "FAAFO Career Platform - Find Your Path to Career Freedom",
  description: "Empowering career transitions through personalized assessments, financial planning, and community support. Take control of your career journey with FAAFO.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background text-foreground`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
        >
          <ErrorBoundary>
            <SessionWrapper>
              {/* Skip to main content link */}
              <a href="#main-content" className="sr-only focus:not-sr-only">Skip to main content</a>

              {/* The header and Navigation component previously here have been removed */}
              {/* The NavigationBar component from page.tsx will now be the sole navigation */}

              <main id="main-content" className="flex-grow">
                {/* Removed container and p-4 from main to allow pages to control their own layout fully */}
                {children}
              </main>

              <Footer />
              <CookieConsentBanner />
            </SessionWrapper>
          </ErrorBoundary>
          <VercelAnalyticsWrapper />
        </ThemeProvider>
      </body>
    </html>
  );
}
