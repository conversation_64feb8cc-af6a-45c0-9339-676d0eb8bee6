import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';

// GET handler to retrieve user achievements
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const includeAll = searchParams.get('includeAll') === 'true';

    if (includeAll) {
      // Get all achievements with user's unlock status
      const allAchievements = await prisma.achievement.findMany({
        where: { isActive: true },
        include: {
          userAchievements: {
            where: { userId: session.user.id },
          },
        },
        orderBy: { createdAt: 'asc' },
      });

      const achievementsWithStatus = allAchievements.map(achievement => ({
        ...achievement,
        isUnlocked: achievement.userAchievements.length > 0,
        unlockedAt: achievement.userAchievements[0]?.unlockedAt || null,
        progress: achievement.userAchievements[0]?.progress || null,
      }));

      return NextResponse.json({
        achievements: achievementsWithStatus,
        total: allAchievements.length,
        unlocked: achievementsWithStatus.filter(a => a.isUnlocked).length,
      });
    } else {
      // Get only user's unlocked achievements
      const whereClause: any = {
        userId: session.user.id,
      };

      const userAchievements = await prisma.userAchievement.findMany({
        where: whereClause,
        include: {
          achievement: {
            where: type ? { type } : undefined,
          },
        },
        orderBy: { unlockedAt: 'desc' },
      });

      const achievements = userAchievements
        .filter(ua => ua.achievement)
        .map(ua => ({
          ...ua.achievement,
          unlockedAt: ua.unlockedAt,
          progress: ua.progress,
        }));

      return NextResponse.json({
        achievements,
        total: achievements.length,
      });
    }
  } catch (error) {
    console.error('Error fetching achievements:', error);
    return NextResponse.json(
      { error: 'Failed to fetch achievements' },
      { status: 500 }
    );
  }
}

// POST handler to unlock an achievement for a user
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { achievementId, progress } = body;

    if (!achievementId) {
      return NextResponse.json(
        { error: 'Achievement ID is required' },
        { status: 400 }
      );
    }

    // Check if achievement exists
    const achievement = await prisma.achievement.findUnique({
      where: { id: achievementId, isActive: true },
    });

    if (!achievement) {
      return NextResponse.json(
        { error: 'Achievement not found' },
        { status: 404 }
      );
    }

    // Check if user already has this achievement
    const existingUserAchievement = await prisma.userAchievement.findUnique({
      where: {
        userId_achievementId: {
          userId: session.user.id,
          achievementId,
        },
      },
    });

    if (existingUserAchievement) {
      return NextResponse.json(
        { error: 'Achievement already unlocked' },
        { status: 400 }
      );
    }

    // Create user achievement
    const userAchievement = await prisma.userAchievement.create({
      data: {
        userId: session.user.id,
        achievementId,
        progress,
      },
      include: {
        achievement: true,
      },
    });

    return NextResponse.json(userAchievement, { status: 201 });
  } catch (error) {
    console.error('Error unlocking achievement:', error);
    return NextResponse.json(
      { error: 'Failed to unlock achievement' },
      { status: 500 }
    );
  }
}

// PUT handler to update achievement progress
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { achievementId, progress } = body;

    if (!achievementId) {
      return NextResponse.json(
        { error: 'Achievement ID is required' },
        { status: 400 }
      );
    }

    // Check if user has this achievement
    const userAchievement = await prisma.userAchievement.findUnique({
      where: {
        userId_achievementId: {
          userId: session.user.id,
          achievementId,
        },
      },
    });

    if (!userAchievement) {
      return NextResponse.json(
        { error: 'User achievement not found' },
        { status: 404 }
      );
    }

    // Update progress
    const updatedUserAchievement = await prisma.userAchievement.update({
      where: {
        userId_achievementId: {
          userId: session.user.id,
          achievementId,
        },
      },
      data: { progress },
      include: {
        achievement: true,
      },
    });

    return NextResponse.json(updatedUserAchievement);
  } catch (error) {
    console.error('Error updating achievement progress:', error);
    return NextResponse.json(
      { error: 'Failed to update achievement progress' },
      { status: 500 }
    );
  }
}

// Helper function to check and unlock achievements based on user activity
export async function checkAndUnlockAchievements(userId: string) {
  try {
    // Get user's learning progress
    const userProgress = await prisma.userLearningProgress.findMany({
      where: { userId },
    });

    const completedResources = userProgress.filter(p => p.status === 'COMPLETED').length;
    const totalRatings = userProgress.filter(p => p.rating).length;

    // Get user's forum activity
    const forumPosts = await prisma.forumPost.count({
      where: { authorId: userId },
    });

    const forumReplies = await prisma.forumReply.count({
      where: { authorId: userId },
    });

    // Get user's goals
    const completedGoals = await prisma.userGoal.count({
      where: { userId, status: 'COMPLETED' },
    });

    // Define achievement criteria
    const achievementChecks = [
      {
        name: 'First Steps',
        description: 'Complete your first learning resource',
        type: 'LEARNING_MILESTONE',
        criteria: { completedResources: 1 },
        check: () => completedResources >= 1,
      },
      {
        name: 'Learning Enthusiast',
        description: 'Complete 10 learning resources',
        type: 'LEARNING_MILESTONE',
        criteria: { completedResources: 10 },
        check: () => completedResources >= 10,
      },
      {
        name: 'Knowledge Seeker',
        description: 'Complete 25 learning resources',
        type: 'LEARNING_MILESTONE',
        criteria: { completedResources: 25 },
        check: () => completedResources >= 25,
      },
      {
        name: 'Community Helper',
        description: 'Make 5 forum posts or replies',
        type: 'COMMUNITY_CONTRIBUTOR',
        criteria: { forumActivity: 5 },
        check: () => (forumPosts + forumReplies) >= 5,
      },
      {
        name: 'Goal Achiever',
        description: 'Complete your first goal',
        type: 'GOAL_ACHIEVER',
        criteria: { completedGoals: 1 },
        check: () => completedGoals >= 1,
      },
      {
        name: 'Reviewer',
        description: 'Rate 5 learning resources',
        type: 'COMPLETION_BADGE',
        criteria: { ratings: 5 },
        check: () => totalRatings >= 5,
      },
    ];

    const newAchievements = [];

    for (const achievementCheck of achievementChecks) {
      if (achievementCheck.check()) {
        // Check if achievement exists
        let achievement = await prisma.achievement.findUnique({
          where: { name: achievementCheck.name },
        });

        if (!achievement) {
          // Create achievement if it doesn't exist
          achievement = await prisma.achievement.create({
            data: {
              name: achievementCheck.name,
              description: achievementCheck.description,
              type: achievementCheck.type as any,
              icon: `achievement-${achievementCheck.type.toLowerCase()}`,
              criteria: achievementCheck.criteria,
              points: 10,
            },
          });
        }

        // Check if user already has this achievement
        const existingUserAchievement = await prisma.userAchievement.findUnique({
          where: {
            userId_achievementId: {
              userId,
              achievementId: achievement.id,
            },
          },
        });

        if (!existingUserAchievement) {
          // Unlock achievement for user
          const userAchievement = await prisma.userAchievement.create({
            data: {
              userId,
              achievementId: achievement.id,
            },
            include: {
              achievement: true,
            },
          });

          newAchievements.push(userAchievement);
        }
      }
    }

    return newAchievements;
  } catch (error) {
    console.error('Error checking achievements:', error);
    return [];
  }
}
