import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { createSuccessResponse, ErrorResponses, withErrorHandling, createPaginationMeta } from '@/lib/api-response';
import { validateInput, paginationSchema, resourceFilterSchema } from '@/lib/validation';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';

export const GET = withErrorHandling(async (request: NextRequest) => {
  // Apply rate limiting
  const rateLimitResult = withRateLimit(rateLimiters.search)(request);
  if (!rateLimitResult.allowed) {
    return ErrorResponses.tooManyRequests();
  }

  const { searchParams } = new URL(request.url);

  // Validate pagination parameters
  const paginationValidation = validateInput(paginationSchema, {
    page: searchParams.get('page'),
    limit: searchParams.get('limit'),
  });

  if (!paginationValidation.success) {
    return ErrorResponses.validationError(paginationValidation.errors);
  }

  const { page, limit } = paginationValidation.data;

  // Validate filter parameters
  const filterValidation = validateInput(resourceFilterSchema, {
    category: searchParams.get('category'),
    skillLevel: searchParams.get('skillLevel'),
    type: searchParams.get('type'),
    cost: searchParams.get('cost'),
    search: searchParams.get('search'),
  });

  if (!filterValidation.success) {
    return ErrorResponses.validationError(filterValidation.errors);
  }

  const { category, skillLevel, type, cost, search } = filterValidation.data;

  const where: Record<string, unknown> = {
    isActive: true,
  };

  if (category && category !== 'all') {
    where.category = category.toUpperCase();
  }

  if (skillLevel && skillLevel !== 'all') {
    where.skillLevel = skillLevel.toUpperCase();
  }

  if (type && type !== 'all') {
    where.type = type.toUpperCase();
  }

  if (cost && cost !== 'all') {
    where.cost = cost.toUpperCase();
  }

  if (search) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
      { author: { contains: search, mode: 'insensitive' } },
    ];
  }

  // Get total count for pagination
  const total = await prisma.learningResource.count({ where });

  // Get paginated resources
  const resources = await prisma.learningResource.findMany({
    where,
    skip: (page - 1) * limit,
    take: limit,
    orderBy: [
      { category: 'asc' },
      { skillLevel: 'asc' },
      { title: 'asc' }
    ],
    include: {
      careerPaths: {
        select: {
          id: true,
          name: true,
          slug: true
        }
      },
      skills: {
        select: {
          id: true,
          name: true
        }
      },
      ratings: {
        select: {
          rating: true
        }
      }
    }
  });

  // Add average rating to each resource
  const resourcesWithRatings = resources.map(resource => ({
    ...resource,
    averageRating: resource.ratings.length > 0
      ? resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length
      : 0,
    totalRatings: resource.ratings.length,
    ratings: undefined, // Remove ratings array from response
  }));

  const meta = createPaginationMeta(page, limit, total);

  return createSuccessResponse(resourcesWithRatings, undefined, meta);
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      title,
      description,
      url,
      type,
      category,
      skillLevel,
      author,
      duration,
      cost = 'FREE',
      format
    } = body;

    // Validate required fields
    if (!title || !description || !url || !type || !category || !skillLevel || !format) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields' 
        },
        { status: 400 }
      );
    }

    const resource = await prisma.learningResource.create({
      data: {
        title,
        description,
        url,
        type: type.toUpperCase(),
        category: category.toUpperCase(),
        skillLevel: skillLevel.toUpperCase(),
        author,
        duration,
        cost: cost.toUpperCase(),
        format: format.toUpperCase()
      }
    });

    return NextResponse.json({
      success: true,
      data: resource
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating learning resource:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create learning resource' 
      },
      { status: 500 }
    );
  }
}
