import { NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import prisma from '@/lib/prisma';
import { signupSchema, validateInput } from '@/lib/validation';
import { createSuccessResponse, ErrorResponses, withErrorHandling } from '@/lib/api-response';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';
import { CONFIG } from '@/lib/config';

export const POST = withErrorHandling(async (request: Request) => {
  // Apply rate limiting
  const rateLimitResult = withRateLimit(rateLimiters.auth)(request as any);
  if (!rateLimitResult.allowed) {
    return ErrorResponses.tooManyRequests();
  }

  const body = await request.json();

  // Validate input
  const validation = validateInput(signupSchema, body);
  if (!validation.success) {
    return ErrorResponses.validationError(validation.errors);
  }

  const { email, password } = validation.data;

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email }
  });

  if (existingUser) {
    return ErrorResponses.conflict("User already exists");
  }

  // Hash the password with secure rounds
  const hashedPassword = await bcrypt.hash(password, CONFIG.SECURITY.BCRYPT_ROUNDS);

  // Create the new user
  const user = await prisma.user.create({
    data: {
      email,
      password: hashedPassword,
    },
    select: {
      id: true,
      email: true,
      createdAt: true,
    },
  });

  return createSuccessResponse(user, "User created successfully");
});