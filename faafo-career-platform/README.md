# FAAFO Career Platform

A comprehensive career guidance platform built with Next.js, TypeScript, and Prisma. FAAFO (Find A Alternative For Occupation) helps users navigate career transitions through personalized assessments, learning resources, and progress tracking.

## 🚀 Features

- **🎯 Career Assessment**: Multi-step assessment to understand user's career goals and preferences
- **🤖 Personalized Recommendations**: AI-driven career path suggestions based on assessment results
- **📚 Learning Resources**: Curated collection of courses, articles, and tutorials with ratings
- **📊 Progress Tracking**: Track learning progress, achievements, and weekly goals
- **💰 Freedom Fund Calculator**: Financial planning tool for career transitions
- **👥 Community Forum**: Connect with other career changers
- **📈 User Dashboard**: Comprehensive overview of progress and recommendations
- **🔐 Secure Authentication**: NextAuth.js with account lockout protection
- **⚡ Performance Optimized**: Rate limiting, pagination, and optimized queries
- **🧪 Comprehensive Testing**: Unit tests, integration tests, and error boundaries

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: SQLite (development), PostgreSQL (production)
- **Authentication**: NextAuth.js with JWT sessions
- **Email**: Resend for transactional emails
- **UI Components**: Radix UI, Lucide Icons
- **Validation**: Zod for type-safe validation
- **Testing**: Jest, React Testing Library
- **Code Quality**: ESLint, TypeScript strict mode

## 🏁 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Git

### Installation

1. **Clone the repository:**
```bash
git clone https://github.com/your-username/faafo-career-platform.git
cd faafo-career-platform
```

2. **Install dependencies:**
```bash
npm install
```

3. **Set up environment variables:**
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
```env
DATABASE_URL="file:./dev.db"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"
RESEND_API_KEY="your-resend-api-key"
EMAIL_FROM="<EMAIL>"
```

4. **Set up the database:**
```bash
npx prisma migrate dev
npx prisma db seed
```

5. **Run the development server:**
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
faafo-career-platform/
├── src/
│   ├── app/                    # Next.js app directory
│   │   ├── api/               # API routes
│   │   │   ├── auth/          # Authentication endpoints
│   │   │   ├── assessment/    # Assessment API
│   │   │   ├── learning-resources/ # Resource management
│   │   │   └── ...
│   │   ├── assessment/        # Assessment pages
│   │   ├── dashboard/         # User dashboard
│   │   ├── login/            # Authentication pages
│   │   └── ...
│   ├── components/            # React components
│   │   ├── ui/               # Reusable UI components
│   │   ├── dashboard/        # Dashboard-specific components
│   │   ├── assessment/       # Assessment components
│   │   └── ...
│   ├── lib/                  # Utility functions and configurations
│   │   ├── auth.tsx          # NextAuth configuration
│   │   ├── prisma.ts         # Database client
│   │   ├── validation.ts     # Zod schemas
│   │   ├── api-response.ts   # Standardized API responses
│   │   ├── rate-limit.ts     # Rate limiting
│   │   └── ...
│   ├── __tests__/            # Test files
│   │   ├── components/       # Component tests
│   │   ├── lib/             # Utility tests
│   │   └── ...
│   └── emails/               # Email templates
├── prisma/                   # Database schema and migrations
│   ├── schema.prisma         # Database schema
│   ├── migrations/           # Database migrations
│   └── seed.ts              # Database seeding
├── public/                   # Static assets
├── docs/                     # Documentation
│   ├── API.md               # API documentation
│   └── ...
├── scripts/                  # Utility scripts
└── __tests__/               # Additional test files
```

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage
- `npm run prisma:seed` - Seed the database
- `npm run test-crud` - Test database operations

## 🔒 Security Features

- **Rate Limiting**: Prevents abuse with configurable limits
- **Input Validation**: Zod schemas for all API inputs
- **Authentication**: Secure session management with NextAuth.js
- **Account Lockout**: Protection against brute force attacks
- **Security Headers**: CSP, HSTS, and other security headers
- **Password Security**: Strong password requirements and secure hashing

## 📊 Performance Features

- **Database Optimization**: Proper indexing and query optimization
- **Pagination**: Cursor-based pagination for large datasets
- **Caching**: Optimized data fetching and caching strategies
- **Error Boundaries**: Graceful error handling in React components
- **Code Splitting**: Automatic code splitting with Next.js

## 🧪 Testing

The project includes comprehensive testing:

```bash
# Run all tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

Test coverage includes:
- Unit tests for utilities and components
- Integration tests for API endpoints
- Error boundary testing
- Validation schema testing

## 📚 API Documentation

See [API.md](docs/API.md) for detailed API documentation including:
- Authentication endpoints
- Assessment management
- Learning resource CRUD operations
- Progress tracking
- Rate limiting information

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Set environment variables in Vercel dashboard
4. Deploy automatically on push

### Manual Deployment

1. Build the application:
```bash
npm run build
```

2. Start the production server:
```bash
npm run start
```

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes and add tests
4. Run tests: `npm run test`
5. Run linting: `npm run lint`
6. Commit your changes: `git commit -am 'Add new feature'`
7. Push to the branch: `git push origin feature/new-feature`
8. Submit a pull request

### Code Quality Standards

- Write TypeScript with strict mode
- Add tests for new features
- Follow existing code patterns
- Use proper error handling
- Document API changes

## 🐛 Troubleshooting

### Common Issues

1. **Database connection errors**: Check your `DATABASE_URL` in `.env.local`
2. **Authentication issues**: Verify `NEXTAUTH_SECRET` and `NEXTAUTH_URL`
3. **Email not sending**: Check `RESEND_API_KEY` configuration
4. **Build errors**: Run `npm run lint` to check for TypeScript errors

### Getting Help

- Check the [docs](docs/) directory for detailed documentation
- Review the [API documentation](docs/API.md)
- Open an issue on GitHub for bugs or feature requests

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- UI components from [Radix UI](https://www.radix-ui.com/)
- Icons from [Lucide](https://lucide.dev/)
- Authentication by [NextAuth.js](https://next-auth.js.org/)
- Database ORM by [Prisma](https://www.prisma.io/)
